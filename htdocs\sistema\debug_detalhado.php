<?php
// Debug da página detalhado
echo "<h2>Debug - Vendas Detalhadas</h2>";

// Verificar se o CodeIgniter está carregando
if (!defined('BASEPATH')) {
    define('BASEPATH', dirname(__FILE__) . '/');
}

// Incluir configurações
require_once 'config.php';

try {
    // Testar conexão com banco
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Conexão com banco de dados OK</p>";
    
    // Simular a consulta do DataTables
    echo "<h3>Testando consulta do get_detalhado()</h3>";
    
    $stmt = $pdo->prepare("
        SELECT 
            s.id, 
            DATE_FORMAT(s.date, '%Y-%m-%d %H:%i:%s') as date, 
            CONCAT(u.first_name, ' ', u.last_name) as user_name,
            s.grand_total
        FROM tec_sales s
        LEFT JOIN tec_users u ON s.created_by = u.id
        ORDER BY s.id DESC
        LIMIT 10
    ");
    $stmt->execute();
    $vendas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✓ Consulta básica OK - " . count($vendas) . " registros encontrados</p>";
    
    if ($vendas) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Data</th><th>Usuário</th><th>Total</th><th>Produtos</th><th>Pagamento</th></tr>";
        
        foreach ($vendas as $venda) {
            echo "<tr>";
            echo "<td>" . $venda['id'] . "</td>";
            echo "<td>" . $venda['date'] . "</td>";
            echo "<td>" . $venda['user_name'] . "</td>";
            echo "<td>R$ " . number_format($venda['grand_total'], 2, ',', '.') . "</td>";
            
            // Testar busca de produtos
            $sale_id = $venda['id'];
            $stmt_items = $pdo->prepare("
                SELECT 
                    si.quantity,
                    p.name as product_name
                FROM tec_sale_items si
                LEFT JOIN tec_products p ON si.product_id = p.id
                WHERE si.sale_id = ?
            ");
            $stmt_items->execute([$sale_id]);
            $items = $stmt_items->fetchAll(PDO::FETCH_ASSOC);
            
            $produtos = array();
            foreach ($items as $item) {
                $quantidade = (float)$item['quantity'];
                if ($quantidade == (int)$quantidade) {
                    $quantidade = (int)$quantidade;
                } else {
                    $quantidade = rtrim(rtrim(number_format($quantidade, 3, '.', ''), '0'), '.');
                }
                $produtos[] = $item['product_name'] . ' (' . $quantidade . ')';
            }
            echo "<td>" . implode(', ', $produtos) . "</td>";
            
            // Testar busca de pagamento
            $stmt_payment = $pdo->prepare("
                SELECT p.paid_by, m.nome
                FROM tec_payments p
                LEFT JOIN tec_meiopagamento m ON p.paid_by = m.cod
                WHERE p.sale_id = ?
                ORDER BY p.id ASC
                LIMIT 1
            ");
            $stmt_payment->execute([$sale_id]);
            $payment = $stmt_payment->fetch(PDO::FETCH_ASSOC);
            
            $paid_by = $payment ? ($payment['nome'] ?: $payment['paid_by']) : 'N/A';
            echo "<td>" . $paid_by . "</td>";
            
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Testar URL do AJAX
    echo "<h3>Testando URL do AJAX</h3>";
    echo "<p>URL esperada: <a href='sales/get_detalhado' target='_blank'>sales/get_detalhado</a></p>";
    
    // Testar se a biblioteca DataTables está carregada
    echo "<h3>Verificando dependências</h3>";
    echo "<p>Verificar se os arquivos JavaScript estão carregando:</p>";
    echo "<ul>";
    echo "<li>jQuery</li>";
    echo "<li>DataTables</li>";
    echo "<li>Funções hrld() e currencyFormat()</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Erro: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>Próximos passos:</strong></p>";
echo "<ol>";
echo "<li>Verificar se a URL sales/get_detalhado está respondendo</li>";
echo "<li>Verificar console do navegador para erros JavaScript</li>";
echo "<li>Verificar se o DataTables está configurado corretamente</li>";
echo "</ol>";
?>

<script>
console.log("Debug script carregado");

// Testar se jQuery está disponível
if (typeof jQuery !== 'undefined') {
    console.log("✓ jQuery está carregado");
} else {
    console.log("✗ jQuery NÃO está carregado");
}

// Testar se DataTables está disponível
if (typeof jQuery !== 'undefined' && jQuery.fn.dataTable) {
    console.log("✓ DataTables está carregado");
} else {
    console.log("✗ DataTables NÃO está carregado");
}
</script>
