<?php
// Teste simples para verificar se a página detalhado está funcionando
echo "<h2>Teste da página Vendas Detalhadas</h2>";

// Verificar se o CodeIgniter está carregando
if (!defined('BASEPATH')) {
    define('BASEPATH', dirname(__FILE__) . '/');
}

// Incluir configurações
require_once 'config.php';

try {
    // Testar conexão com banco
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Conexão com banco de dados OK</p>";
    
    // Testar consulta básica de vendas
    $stmt = $pdo->prepare("
        SELECT 
            s.id, 
            s.date, 
            s.customer_name, 
            s.grand_total,
            CONCAT(u.first_name, ' ', u.last_name) as user_name
        FROM tec_sales s
        LEFT JOIN tec_users u ON s.created_by = u.id
        ORDER BY s.id DESC
        LIMIT 5
    ");
    $stmt->execute();
    $vendas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✓ Consulta de vendas OK - " . count($vendas) . " registros encontrados</p>";
    
    if ($vendas) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Data</th><th>Cliente</th><th>Usuário</th><th>Total</th></tr>";
        foreach ($vendas as $venda) {
            echo "<tr>";
            echo "<td>" . $venda['id'] . "</td>";
            echo "<td>" . $venda['date'] . "</td>";
            echo "<td>" . $venda['customer_name'] . "</td>";
            echo "<td>" . $venda['user_name'] . "</td>";
            echo "<td>R$ " . number_format($venda['grand_total'], 2, ',', '.') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Testar consulta de itens de venda
    if ($vendas) {
        $sale_id = $vendas[0]['id'];
        $stmt = $pdo->prepare("
            SELECT 
                si.quantity,
                p.name as product_name
            FROM tec_sale_items si
            LEFT JOIN tec_products p ON si.product_id = p.id
            WHERE si.sale_id = ?
        ");
        $stmt->execute([$sale_id]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p style='color: green;'>✓ Consulta de itens OK - " . count($items) . " itens para venda #$sale_id</p>";
    }
    
    // Testar consulta de métodos de pagamento
    $stmt = $pdo->prepare("SELECT cod, nome FROM tec_meiopagamento LIMIT 5");
    $stmt->execute();
    $payment_methods = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✓ Consulta de métodos de pagamento OK - " . count($payment_methods) . " métodos encontrados</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Erro: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='sales/detalhado'>Ir para página Vendas Detalhadas</a></p>";
?>
