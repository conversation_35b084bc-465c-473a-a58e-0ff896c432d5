<?php
// Teste direto da URL AJAX
echo "<h2>Teste da URL AJAX - get_detalhado</h2>";

echo "<p>Testando diferentes URLs:</p>";
echo "<ul>";
echo "<li><a href='sales/get_detalhado' target='_blank'>sales/get_detalhado</a></li>";
echo "<li><a href='index.php/sales/get_detalhado' target='_blank'>index.php/sales/get_detalhado</a></li>";
echo "<li><a href='sistema/sales/get_detalhado' target='_blank'>sistema/sales/get_detalhado</a></li>";
echo "</ul>";

echo "<h3>Teste com parâmetros DataTables</h3>";
echo "<p>Simulando requisição POST do DataTables:</p>";

// Simular dados que o DataTables envia
$datatables_params = array(
    'draw' => 1,
    'start' => 0,
    'length' => 10,
    'search[value]' => '',
    'order[0][column]' => 0,
    'order[0][dir]' => 'desc'
);

echo "<pre>";
echo "Parâmetros que o DataTables envia:\n";
print_r($datatables_params);
echo "</pre>";

echo "<h3>JavaScript para testar AJAX</h3>";
?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    console.log("Testando AJAX...");
    
    // Testar diferentes URLs
    var urls = [
        'sales/get_detalhado',
        'index.php/sales/get_detalhado',
        '/sistema/sales/get_detalhado'
    ];
    
    urls.forEach(function(url, index) {
        console.log("Testando URL " + (index + 1) + ": " + url);
        
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                draw: 1,
                start: 0,
                length: 10,
                'search[value]': '',
                'order[0][column]': 0,
                'order[0][dir]': 'desc'
            },
            success: function(response) {
                console.log("✓ Sucesso para " + url);
                console.log("Resposta:", response);
                
                // Adicionar resultado na página
                $('#results').append('<div style="border: 1px solid green; margin: 10px; padding: 10px;">' +
                    '<h4 style="color: green;">✓ Sucesso: ' + url + '</h4>' +
                    '<pre>' + JSON.stringify(response, null, 2) + '</pre>' +
                    '</div>');
            },
            error: function(xhr, status, error) {
                console.log("✗ Erro para " + url + ": " + error);
                console.log("Status:", status);
                console.log("Response:", xhr.responseText);
                
                // Adicionar erro na página
                $('#results').append('<div style="border: 1px solid red; margin: 10px; padding: 10px;">' +
                    '<h4 style="color: red;">✗ Erro: ' + url + '</h4>' +
                    '<p>Status: ' + status + '</p>' +
                    '<p>Erro: ' + error + '</p>' +
                    '<pre>' + xhr.responseText + '</pre>' +
                    '</div>');
            }
        });
    });
});
</script>

<div id="results">
    <h3>Resultados dos testes AJAX:</h3>
</div>

<hr>
<p><a href="sales/detalhado">Voltar para página Vendas Detalhadas</a></p>
